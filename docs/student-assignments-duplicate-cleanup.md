# სტუდენტების დუბლირებული ნიშნების გასუფთავება

## პრობლემის აღწერა

`student_assignments` ცხრილში ზოგჯერ ორმაგდება სტუდენტების ნიშნები. ეს ნიშნავს, რომ ერთსა და იმავე სტუდენტს (`student_id`), ერთსა და იმავე სილაბუსზე (`syllabus_id`) და ერთსა და იმავე დავალებაზე (`assignment_id`) რამდენიმე ჩანაწერი ექმნება.

## გადაწყვეტა

### 1. ავტომატური გასუფთავების კომანდი

შექმნილია `AssignmentDuplicateClearCommand` კომანდი, რომელიც:

- **კომანდის სახელი**: `assignment:clean-duplicates`
- **აღწერა**: სტუდენტების დუბლირებული ნიშნების გასუფთავება student_assignments ცხრილში
- **გაშვების დრო**: ყოველ ღამე 03:00 საათზე ავტომატურად

### 2. კომანდის ფუნქციონალი

#### Dry Run რეჟიმი
```bash
php artisan assignment:clean-duplicates --dry-run
```
- აჩვენებს რამდენი და რა ჩანაწერი წაიშლება
- არაფერს არ შლის რეალურად
- უსაფრთხო ტესტირებისთვის

#### რეალური გასუფთავება
```bash
php artisan assignment:clean-duplicates
```
- ეკითხება დადასტურებას
- შლის დუბლირებულ ჩანაწერებს
- ტოვებს ყველაზე ბოლო (უახლეს) ჩანაწერს
- ლოგავს შედეგს

### 3. ლოგიკა

კომანდი იყენებს შემდეგ SQL ლოგიკას:

```sql
DELETE sa1 FROM student_assignments sa1
INNER JOIN student_assignments sa2
ON sa1.student_id = sa2.student_id
AND sa1.assignment_id = sa2.assignment_id
AND sa1.syllabus_id = sa2.syllabus_id
AND sa1.id > sa2.id
```

ეს ნიშნავს:
- ეძებს ჩანაწერებს რომლებსაც აქვთ იგივე `student_id`, `assignment_id`, `syllabus_id`
- შლის ყველა ჩანაწერს გარდა ყველაზე პირველისა (ყველაზე პატარა `id`-ით)

### 4. პრევენცია - უნიკალური ინდექსი

შექმნილია მიგრაცია რომელიც ამატებს უნიკალურ ინდექსს:

```php
$table->unique(['student_id', 'assignment_id', 'syllabus_id'], 'unique_student_assignment_syllabus');
```

ეს ინდექსი:
- ავიცილებს თავიდან ახალი დუბლირებული ჩანაწერების შექმნას
- მონაცემთა ბაზის დონეზე უზრუნველყოფს უნიკალურობას

### 5. მიგრაციის გაშვება

**მნიშვნელოვანი**: მიგრაციის გაშვებამდე აუცილებლად გაუშვით გასუფთავების კომანდი!

```bash
# ჯერ გაასუფთავეთ არსებული დუბლირებული ჩანაწერები
php artisan assignment:clean-duplicates

# შემდეგ გაუშვით მიგრაცია
php artisan migrate
```

### 6. მონიტორინგი

კომანდი ლოგავს თავის მუშაობას:
- ლოგის ფაილში ჩაიწერება რამდენი ჩანაწერი წაიშალა
- შეგიძლიათ შეამოწმოთ `storage/logs/laravel.log` ფაილში

### 7. Schedule

კომანდი ავტომატურად გაეშვება ყოველ ღამე 03:00 საათზე:

```php
$schedule->command(AssignmentDuplicateClearCommand::class)->dailyAt('03:00');
```

### 8. ხელით გაშვება

ნებისმიერ დროს შეგიძლიათ ხელით გაუშვათ:

```bash
# ტესტირებისთვის (არაფერს არ შლის)
php artisan assignment:clean-duplicates --dry-run

# რეალური გასუფთავებისთვის
php artisan assignment:clean-duplicates
```

## უსაფრთხოება

- კომანდი ყოველთვის ტოვებს ყველაზე ძველ ჩანაწერს (პატარა ID-ით)
- Dry-run რეჟიმი უსაფრთხო ტესტირებისთვის
- დადასტურების მოთხოვნა რეალური წაშლისას
- ლოგირება ყველა ოპერაციისა

## მომავალი გაუმჯობესებები

1. **ალერტები**: შეიძლება დაემატოს ალერტები თუ ძალიან ბევრი დუბლირებული ჩანაწერი მოიძებნება
2. **სტატისტიკა**: ყოველდღიური რეპორტი რამდენი ჩანაწერი წაიშალა
3. **ბექაპი**: ავტომატური ბექაპი წაშლამდე (თუ საჭირო იქნება)
